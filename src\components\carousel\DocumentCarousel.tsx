import React, { useCallback, useEffect, useRef } from 'react'
import {
  EmblaCarouselType,
  EmblaOptionsType
} from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import {
  NextButton,
  PrevButton,
  usePrevNextButtons
} from './EmblaCarouselArrowButtons'
import { DotButton, useDotButton } from './EmblaCarouselDotButton'
import { useTranslation } from '@/hooks/use-translation'

type DocumentType = {
  path: string
  name: string
  category: string
}

type PropType = {
  documents: DocumentType[]
  options?: EmblaOptionsType
  onSlideChange?: (index: number) => void
  onApiReady?: (api: EmblaCarouselType, resetAutoplay: () => void) => void
}

const DocumentCarousel: React.FC<PropType> = (props) => {
  const { documents, options, onSlideChange, onApiReady } = props
  const { t } = useTranslation('homepage')
  const autoplayIntervalRef = useRef<NodeJS.Timeout | null>(null)

  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'center',
      containScroll: false,
      ...options
    }
  )

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi)

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick
  } = usePrevNextButtons(emblaApi)

  // Autoplay functionality
  const startAutoplay = useCallback(() => {
    if (autoplayIntervalRef.current) {
      clearInterval(autoplayIntervalRef.current)
    }
    autoplayIntervalRef.current = setInterval(() => {
      if (emblaApi) {
        emblaApi.scrollNext()
      }
    }, 3000)
  }, [emblaApi])

  const resetAutoplay = useCallback(() => {
    if (autoplayIntervalRef.current) {
      clearInterval(autoplayIntervalRef.current)
    }
    startAutoplay()
  }, [startAutoplay])

  // Start autoplay when emblaApi is ready
  useEffect(() => {
    if (emblaApi) {
      startAutoplay()
      return () => {
        if (autoplayIntervalRef.current) {
          clearInterval(autoplayIntervalRef.current)
        }
      }
    }
  }, [emblaApi, startAutoplay])

  // Notify parent component when slide changes and provide API
  useEffect(() => {
    if (emblaApi) {
      if (onApiReady) {
        onApiReady(emblaApi, resetAutoplay)
      }
      if (onSlideChange) {
        const onSelect = () => {
          onSlideChange(emblaApi.selectedScrollSnap())
        }
        emblaApi.on('select', onSelect)
        onSelect() // Call once to set initial state
        return () => {
          emblaApi.off('select', onSelect)
        }
      }
    }
  }, [emblaApi, onSlideChange, onApiReady, resetAutoplay])





  return (
    <div className="w-full max-w-6xl mx-auto px-2 sm:px-4">
      {/* Carousel Container */}
      <div className="flex items-center gap-1 sm:gap-2 md:gap-4">
        {/* Left Arrow */}
        <div className="flex-shrink-0">
          <div
            onClick={onPrevButtonClick}
            className={`w-6 h-12 sm:w-8 sm:h-16 md:w-12 md:h-20 lg:w-16 lg:h-24 bg-transparent hover:bg-gray-200 rounded-lg flex items-center justify-center cursor-pointer transition-colors ${
              prevBtnDisabled ? 'opacity-30 cursor-not-allowed' : ''
            }`}
          >
            <PrevButton onClick={() => {}} disabled={prevBtnDisabled} />
          </div>
        </div>

        {/* Carousel */}
        <div className="embla overflow-hidden flex-1 min-w-0" ref={emblaRef}>
          <div className="embla__container flex">
            {documents.map((doc, index) => {
              const isSelected = index === selectedIndex
              return (
                <div
                  className="embla__slide flex-[0_0_90%] xs:flex-[0_0_85%] sm:flex-[0_0_75%] md:flex-[0_0_60%] lg:flex-[0_0_45%] min-w-0 px-1 sm:px-2"
                  key={index}
                >
                  {/* Document type label - positioned above the image */}
                  <div className="text-center mb-2">
                    <h3 className="text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl font-bold text-purple-600 leading-tight">
                      {t(`documentNames.${doc.name}`)}
                    </h3>
                  </div>

                  <div
                    className="h-full relative transition-all duration-300 ease-in-out"
                    style={{
                      transform: isSelected ? 'scale(1)' : 'scale(0.7)',
                      opacity: isSelected ? 1 : 0.5,
                    }}
                  >
                    <div className="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200">
                      <div className="aspect-[4/5] p-1 sm:p-2">
                        <img
                          src={doc.path}
                          alt={doc.name}
                          className="w-full h-full object-contain"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Right Arrow */}
        <div className="flex-shrink-0">
          <div
            onClick={onNextButtonClick}
            className={`w-6 h-12 sm:w-8 sm:h-16 md:w-12 md:h-20 lg:w-16 lg:h-24 bg-transparent hover:bg-gray-200 rounded-lg flex items-center justify-center cursor-pointer transition-colors ${
              nextBtnDisabled ? 'opacity-30 cursor-not-allowed' : ''
            }`}
          >
            <NextButton onClick={() => {}} disabled={nextBtnDisabled} />
          </div>
        </div>
      </div>

      {/* Dots Navigation */}
      <div className="flex justify-center mt-4 gap-2">
        {scrollSnaps.map((_, index) => (
          <DotButton
            key={index}
            onClick={() => onDotButtonClick(index)}
            className={`touch-manipulation ${
              index === selectedIndex
                ? 'bg-gradient-to-r from-purple-600 via-blue-600 to-pink-600'
                : 'bg-gray-300 hover:bg-gray-400 active:bg-gray-500'
            }`}
          />
        ))}
      </div>
    </div>
  )
}

export default DocumentCarousel
